import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('app')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ status: 200, description: '服务信息' })
  getServiceInfo() {
    return this.appService.getServiceInfo();
  }

  @Get('version')
  @ApiOperation({ summary: '获取服务版本' })
  @ApiResponse({ status: 200, description: '服务版本信息' })
  getVersion() {
    return this.appService.getVersion();
  }
}
