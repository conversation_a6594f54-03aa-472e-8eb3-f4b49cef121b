import { Injectable, Logger, NotFoundException } from '@nestjs/common';

@Injectable()
export class PartnersService {
  private readonly logger = new Logger(PartnersService.name);
  private partners: Map<string, any> = new Map();
  private applications: Map<string, any> = new Map();

  async getPartners(filters: any = {}) {
    const allPartners = Array.from(this.partners.values());
    
    let filteredPartners = allPartners;
    
    if (filters.type) {
      filteredPartners = filteredPartners.filter(p => p.type === filters.type);
    }
    
    if (filters.tier) {
      filteredPartners = filteredPartners.filter(p => p.tier === filters.tier);
    }
    
    if (filters.status) {
      filteredPartners = filteredPartners.filter(p => p.status === filters.status);
    }
    
    return {
      partners: filteredPartners,
      total: filteredPartners.length,
      filters
    };
  }

  async getPartner(id: string) {
    const partner = this.partners.get(id);
    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }
    return partner;
  }

  async createApplication(application: any) {
    const applicationId = `app_${Date.now()}`;
    
    const newApplication = {
      ...application,
      applicationId,
      status: 'pending',
      submittedAt: new Date(),
      reviewedAt: null,
      approvedAt: null
    };
    
    this.applications.set(applicationId, newApplication);
    
    this.logger.log(`合作伙伴申请创建: ${applicationId} - ${application.name}`);
    
    return {
      applicationId,
      status: 'pending',
      message: '申请已提交，等待审核'
    };
  }

  async updatePartner(id: string, updateData: any) {
    const partner = this.partners.get(id);
    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }
    
    const updatedPartner = {
      ...partner,
      ...updateData,
      updatedAt: new Date()
    };
    
    this.partners.set(id, updatedPartner);
    
    this.logger.log(`合作伙伴更新: ${id}`);
    
    return updatedPartner;
  }

  async deletePartner(id: string) {
    const partner = this.partners.get(id);
    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }
    
    this.partners.delete(id);
    
    this.logger.log(`合作伙伴删除: ${id}`);
    
    return {
      message: '合作伙伴已删除',
      deletedId: id
    };
  }

  async getPartnerPerformance(id: string) {
    const partner = this.partners.get(id);
    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }
    
    return {
      partnerId: id,
      performance: partner.performance || {
        revenue: 0,
        customers: 0,
        projects: 0,
        satisfaction: 0,
        support_rating: 0,
        certification_compliance: 0,
        last_evaluation: new Date()
      },
      trends: {
        revenue_trend: 'stable',
        customer_trend: 'growing',
        satisfaction_trend: 'improving'
      }
    };
  }

  async addCertification(id: string, certification: any) {
    const partner = this.partners.get(id);
    if (!partner) {
      throw new NotFoundException(`合作伙伴 ${id} 不存在`);
    }
    
    const certificationId = `cert_${Date.now()}`;
    const newCertification = {
      ...certification,
      certificationId,
      issuedAt: new Date(),
      status: 'valid'
    };
    
    if (!partner.certifications) {
      partner.certifications = [];
    }
    
    partner.certifications.push(newCertification);
    this.partners.set(id, partner);
    
    this.logger.log(`认证添加: ${id} - ${certification.name}`);
    
    return {
      certificationId,
      message: '认证添加成功'
    };
  }
}
